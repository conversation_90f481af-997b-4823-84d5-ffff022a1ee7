<template>
    <div class="vsSingUp">
        <!-- Tab 导航 -->
        <div class="tab-container">
            <div class="tab-header">
                <div class="tab-item" :class="{ active: activeTab === 'signin' }" @click="activeTab = 'signin'">
                    签到
                </div>
                <div class="tab-item" :class="{ active: activeTab === 'register' }" @click="activeTab = 'register'">
                    注册
                </div>
                <div class="tab-item" :class="{ active: activeTab === 'login' }" @click="activeTab = 'login'">
                    登录
                </div>
            </div>
        </div>

        <!-- 签到 Tab 内容 -->
        <div v-show="activeTab === 'signin'" class="tab-content">
            <div class="title">扫码签到</div>
            <template v-if="!sended">
                <div class="formBox">
                    <el-form ref="form" :model="queryForm" label-width="80px" :rules="rules">
                        <el-form-item label="姓名：" prop="userName">
                            <el-input v-model.trim="queryForm.userName" placeholder="请输入您的姓名" style="width: 100%" />
                        </el-form-item>
                        <el-form-item label="手机号：" prop="phone">
                            <el-input v-model.trim="queryForm.phone" placeholder="请输入您的手机号" style="width: 100%" />
                        </el-form-item>
                    </el-form>
                </div>
                <div class="buttonGroup">
                    <el-button type="primary" @click="onSubmit" :loading="loading">提交</el-button>
                </div>
            </template>
            <template v-else>
                <div class="sended">
                    <img src="@/assets/images/simulatedVS/singUpSuccess.png" alt="" />
                    <div>签到成功！</div>
                </div>
            </template>
        </div>

        <!-- 注册 Tab 内容 -->
        <div v-show="activeTab === 'register'" class="tab-content">
            <div class="title">用户注册</div>
            <template v-if="!registerSended">
                <div class="formBox">
                    <el-form ref="registerForm" :model="registerForm" label-width="100px" :rules="registerRules">
                        <el-form-item label="用户名：" prop="userName">
                            <el-input v-model.trim="registerForm.userName" placeholder="请输入用户名" style="width: 100%" />
                        </el-form-item>
                        <el-form-item label="用户昵称：" prop="nickName">
                            <el-input v-model.trim="registerForm.nickName" placeholder="请输入用户昵称" style="width: 100%" />
                        </el-form-item>
                        <el-form-item label="密码：" prop="password">
                            <el-input v-model.trim="registerForm.password" type="password" placeholder="请输入密码"
                                style="width: 100%" />
                        </el-form-item>
                        <el-form-item label="角色：" prop="drillRole">
                            <el-select v-model="registerForm.drillRole" placeholder="请选择角色" style="width: 100%">
                                <el-option label="红方" value="red"></el-option>
                                <el-option label="蓝方" value="blue"></el-option>
                                <el-option label="观众" value="spectator"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="buttonGroup">
                    <el-button type="primary" @click="onRegisterSubmit" :loading="registerLoading">注册</el-button>
                </div>
            </template>
            <template v-else>
                <div class="sended">
                    <img src="@/assets/images/simulatedVS/singUpSuccess.png" alt="" />
                    <div>注册成功</div>
                    <el-button class="btn-group" type="primary" size="large"
                        @click="goToLogin">前往登录页面</el-button>
                </div>
            </template>
        </div>

        <!-- 登录 Tab 内容 -->
        <div v-show="activeTab === 'login'" class="tab-content">
            <div class="title">用户登录</div>
            <div class="login-content">
                <div class="login-button-container">
                    <el-button type="primary" size="large" @click="goToLogin">前往登录页面</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { drillSingUpApi, registerApi } from "@/api/simulatedVS/index.js";

export default {
    data() {
        return {
            activeTab: 'signin', // 当前激活的 tab
            loading: false,
            queryForm: {
                userName: '',
                phone: '',
            },
            rules: {
                userName: [
                    { required: true, message: '请输入姓名', trigger: 'change' },
                ],
                phone: [
                    { required: false, message: '请输入手机号码', trigger: 'blur' },
                    { validator: this.validatePhoneNumber, trigger: 'change' }
                ],
            },
            sended: false,// 是否已提交
            // 注册相关数据
            registerLoading: false,
            registerForm: {
                userName: '',
                nickName: '',
                password: '',
                drillRole: '',
            },
            registerRules: {
                userName: [
                    { required: true, message: '请输入用户名', trigger: 'change' },
                ],
                nickName: [
                    { required: true, message: '请输入用户昵称', trigger: 'change' },
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'change' },
                    { min: 6, message: '密码长度不能少于6位', trigger: 'change' }
                ],
                drillRole: [
                    { required: true, message: '请选择角色', trigger: 'change' },
                ],
            },
            registerSended: false,// 注册是否已提交
        };
    },
    computed: {
        drillTaskId() {
            return this.$route.query.drillTaskId
        },
    },
    created() {
        document.title = '扫码签到'
    },
    methods: {
        onSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    let params = {
                        drillTaskId: this.drillTaskId,
                        ...this.queryForm,
                    };

                    console.log('params', params)
                    this.loading = true;
                    drillSingUpApi(params).then((res) => {
                        this.$message.success('签到成功')
                        this.loading = false;
                        this.sended = true;
                    }).catch((err) => {
                        this.loading = false;
                    })
                }
            })
        },

        // 自定义手机号码验证函数
        validatePhoneNumber(rule, value, callback) {
            // 检查值是否为空，如果是，则直接通过验证
            if (!value) {
                callback();
            } else {
                const phonePattern = /^1[1-9]\d{9}$/;
                // 如果值不为空，则继续进行手机号码格式验证
                if (!phonePattern.test(value)) {
                    callback(new Error('请输入正确的11位手机号码'));
                } else {
                    callback();
                }
            }
        },
        handelClose() {
            window.close();
        },

        // 注册提交
        onRegisterSubmit() {
            this.$refs.registerForm.validate(valid => {
                if (valid) {
                    let params = {
                        drillTaskId: this.drillTaskId,
                        ...this.registerForm,
                        password: this.$md5(this.registerForm.password)
                    };

                    console.log('register params', params)
                    this.registerLoading = true;
                    // 这里可以调用注册 API
                    registerApi(params).then((res) => {
                        this.$message.success(res.data.message||res.msg)
                        this.registerLoading = false;
                        this.registerSended = true;
                    }).catch((err) => {
                        this.registerLoading = false;
                    })
                }
            })
        },

        // 跳转到登录页面
        goToLogin() {
            this.$message.info('跳转到登录页面');
            this.$router.push({ path: '/login' })
        },

    },
};
</script>

<style lang="scss" scoped>
.vsSingUp {
    width: 100%;
    height: 100%;
    position: relative;

    // Tab 容器样式
    .tab-container {
        .tab-header {
            display: flex;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e4e7ed;

            .tab-item {
                flex: 1;
                padding: 15px 20px;
                text-align: center;
                cursor: pointer;
                font-size: 16px;
                font-weight: 500;
                color: #606266;
                background-color: #f5f5f5;
                border-bottom: 3px solid transparent;
                transition: all 0.3s ease;

                &:hover {
                    color: #409eff;
                    background-color: #ecf5ff;
                }

                &.active {
                    color: #409eff;
                    background-color: #ffffff;
                    border-bottom-color: #409eff;
                    font-weight: 600;
                }

                &:not(:last-child) {
                    border-right: 1px solid #e4e7ed;
                }
            }
        }
    }

    // Tab 内容样式
    .tab-content {
        .title {
            font-size: 20px;
            font-weight: 600;
            padding: 20px;
            // background-color: #EDEDED;
            text-align: center;
        }

        .formBox {
            padding: 40px 20px;
        }

        .buttonGroup {
            width: 100%;
            position: absolute;
            bottom: 20px;
            padding-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;

            background: #FFFFFF;
            box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05);
        }

        .sended {
            display: flex;
            flex-flow: column;
            justify-content: center;
            align-items: center;

            img {
                max-width: 300px;
                width: 100%;
                padding: 30px 30px 20px 30px;
            }

            div {
                font-size: 22px;
                font-weight: 600;
                text-align: center;
            }

            .btn-group {
                margin-top: 20px;
                width: fit-content;
            }
        }

        // 登录页面特殊样式
        .login-content {
            padding: 60px 20px;

            .login-button-container {
                display: flex;
                justify-content: center;
                align-items: center;

                .el-button {
                    padding: 15px 40px;
                    font-size: 16px;
                }
            }
        }
    }
}
</style>